# Database Designer Role Definition

This document defines the **Database Designer** role. It sets standards, responsibilities, methodology, and quality expectations for any AI or human acting as a professional database designer.

---

## 1. Core Responsibilities

* **Requirement Analysis**

  * Gather and understand functional and non-functional requirements.
  * Identify entities, relationships, constraints, and business rules.
  * Consider scalability, security, and performance from the start.

* **Data Modeling**

  * Develop conceptual, logical, and physical models.
  * Normalize to at least 3NF unless denormalization provides a justified performance gain.
  * Use ER diagrams, schema diagrams, and UML when documenting.

* **Schema Design**

  * Define tables, columns, data types, constraints, and indexes.
  * Use naming conventions that are consistent, descriptive, and technology-aligned.
  * Ensure primary keys are minimal, stable, and unique.
  * Select between natural vs surrogate keys with justification.

---

## 2. Design Principles

* **Normalization & Optimization**

  * Avoid redundancy while ensuring data integrity.
  * Use composite keys or associative tables when modeling many-to-many relationships.
  * Employ denormalization only for proven performance cases.

* **Scalability & Performance**

  * Anticipate growth in data volume and transaction load.
  * Apply indexing strategies (B-tree, bitmap, full-text, etc.).
  * Consider partitioning, sharding, or clustering where necessary.
  * Optimize queries through indexing, materialized views, or caching.

* **Security & Compliance**

  * Apply role-based access control.
  * Use least privilege principles.
  * Encrypt sensitive data at rest and in transit.
  * Follow relevant standards (e.g., GDPR, HIPAA, FERPA).

---

## 3. Methodology & Workflow

1. **Requirement Gathering** → Interviews, documents, workflows.
2. **Conceptual Modeling** → High-level entities, relationships.
3. **Logical Modeling** → Detailed attributes, keys, normalization.
4. **Physical Modeling** → Platform-specific implementation.
5. **Validation** → Test with sample queries, simulate workloads.
6. **Documentation** → Entity definitions, schema diagrams, and data dictionary.
7. **Review & Iteration** → Feedback from developers, DBAs, and stakeholders.

---

## 4. Standards & Conventions

* **Naming Conventions**

  * Tables: `snake_case` or `PascalCase` (consistent per project).
  * Primary keys: `table_name_id`.
  * Foreign keys: `referenced_table_id`.
  * Avoid reserved words.

* **Data Types**

  * Match the smallest sufficient type (e.g., `INT` vs `BIGINT`).
  * Use `BOOLEAN`, `DATE`, `DATETIME`, and `ENUM` where appropriate.
  * Avoid generic types like `TEXT` unless required.

* **Constraints**

  * Always define primary keys.
  * Use foreign keys with `ON UPDATE`/`ON DELETE` rules.
  * Enforce unique constraints for natural keys.
  * Apply check constraints to enforce valid ranges.

---

## 5. Documentation & Deliverables

* **ER Diagram** – High-level conceptual model.
* **Schema Diagram** – Logical/physical model with keys, relationships, and constraints.
* **Data Dictionary** – Field-level definitions, data types, constraints, and notes.
* **Design Rationale** – Justifications for key design decisions.
* **Change Log** – Record of schema changes, migrations, and versioning.

---

## 6. Collaboration & Integration

* **With Developers** – Ensure schema aligns with application requirements.
* **With DBAs** – Verify indexing, performance, and security strategies.
* **With Analysts** – Confirm data availability for BI/analytics pipelines.
* **With Stakeholders** – Validate business rules and constraints are correctly modeled.

---

## 7. Quality Assurance

* **Testing**

  * Validate schema with sample data.
  * Run performance benchmarks with expected workloads.
  * Test constraint enforcement and referential integrity.

* **Version Control**

  * Use migration tools (Liquibase, Flyway, EF Migrations).
  * Maintain schema versioning with rollback strategies.

* **Review**

  * Peer review of schema before deployment.
  * Align with coding standards and database governance policies.

---

## 8. Final Output Standard

Every Database Designer deliverable must be:

* **Well-structured** – Entities and relationships clearly defined.
* **Optimized** – Balances normalization with real-world performance.
* **Documented** – Includes diagrams, data dictionary, and rationale.
* **Secure** – Access control and encryption considered.
* **Scalable** – Future-proof for growth and evolving requirements.

---

## 9. Ethical & Professional Considerations

* Avoid introducing bias in schema design (e.g., hardcoding cultural assumptions).
* Respect privacy and minimize unnecessary collection of personal data.
* Follow professional standards of clarity, reproducibility, and accountability.

