
# QCU OPAC & Library Management System: Requirements

**Version:** 1.0
**Date:** 2025-09-28

---

### 1. Overview

This document outlines the functional and non-functional requirements for a new Online Public Access Catalog (OPAC) and integrated library management system for Quezon City University. The system will provide students, faculty, and alumni with a digital portal to search the library's catalog, borrow materials, and reserve rooms. It will also equip library staff with tools for circulation, cataloging, and administration.

### 2. User Personas & Roles

| Persona | Role | Key Permissions & Actions |
| :--- | :--- | :--- |
| **Ordinary User** | Student, Alumni, Faculty | - Search catalog (books, materials)<br>- View item details & availability<br>- Place holds on books<br>- Borrow up to 3 books<br>- Reserve collaborative rooms (1-hour slots)<br>- View personal borrowing history & fines<br>- Reset own password |
| **Librarian** | Admin | - All Ordinary User actions<br>- Manage circulation (check-in/out via barcode)<br>- Catalog management (add/edit/delete book records)<br>- Manage room reservations<br>- Post and manage events/announcements (draft)<br>- Handle fine payments (cash at desk)<br>- Manually override transactions |
| **Head Librarian** | Super Admin | - All Librarian actions<br>- Access to system dashboards & analytics<br>- View system audit logs<br>- Approve events/announcements for publishing |

### 3. System Workflows

#### 3.1. User Authentication
- **Login:** Users log in with credentials from the central QCU Enrollment System. No separate registration is needed.
- **Data Sync:** On first login, user data (Name, ID, Program, Email, Year) is fetched via API.
- **Password Management:**
    - The library system will use the enrollment system's password for initial login.
    - Users can change their password within the library system. This new password will be stored *separately* and will not sync back to the enrollment system. This is a deliberate choice to isolate the systems, though it requires users to manage two separate passwords if they change one.
    - A "Forgot Password" flow will be available for the library system password.

#### 3.2. Book Circulation
- **Search:** Users can perform basic and advanced searches (Title, Author, Subject, ISBN). Results display as cover thumbnails with title and a brief description.
- **Borrowing:**
    - Loan period: **14 days**.
    - Renewals must be done in person at the library, not online.
    - A user can borrow a maximum of **3 books** at a time.
- **Holds:**
    - Users can place a hold on a book that is currently checked out.
    - A hold automatically expires if the book is not picked up within **1 day** of becoming available.
- **Fines:**
    - Overdue fine: **₱20.00 per day, per book**.
    - Fines are paid in cash at the circulation desk.
    - Fine data will be pushed to the enrollment system via API for record-keeping.

#### 3.3. Room Reservation
- **Rooms:** 7 collaborative rooms are available, named "Collaborative Room A" through "G".
- **Booking:**
    - A user can reserve a room for a **1-hour slot** only.
    - A user can only have **one active room reservation per day**.
    - Reservations require a minimum of 5 people and a maximum of 10. The reserving user's name is captured, with fields for other participants.
- **No-Show Policy:** A reservation is automatically canceled if the user does not check in within **10 minutes** of the start time, freeing the room.

#### 3.4. Content Management
- **Events/Announcements:**
    - A Librarian can draft posts (title, date, description, image).
    - The Head Librarian must approve posts before they are published on the homepage.
    - No RSVP functionality is required.

### 4. Functional Requirements

- **FR1: Catalog:** Searchable, filterable, with detailed views for each item.
- **FR2: Circulation:** Barcode-driven check-in/out, hold management, and fine calculation.
- **FR3: User Accounts:** Profile view (borrowing history, fines), password reset.
- **FR4: Room Reservations:** Booking interface, availability calendar, and automated no-show cancellations.
- **FR5: Admin Dashboard:** (For Head Librarian) KPIs on circulation, user activity, and popular items.
- **FR6: Logging:** Detailed audit trails of all significant user and admin actions.

### 5. Non-Functional Requirements

- **NFR1: Performance:** System must be fast and responsive, especially search.
- **NFR2: Security:** Adherence to data privacy principles, secure authentication, and protection against common web vulnerabilities.
- **NFR3: Availability:**
    - If the enrollment system API is down, a "System Unavailable" message will be shown for login/fine-sync dependent features.
    - The system will have regular database backups every **2 weeks**.
- **NFR4: Usability:** The UI must be intuitive and responsive, designed for a primary desktop screen size of **1440x1024**.
- **NFR5: Data Integrity:** The system must prevent double-booking of rooms at the API level.

### 6. Integrations

- **INT1: QCU Enrollment System (API):** For user authentication and fetching user data. Fines will be pushed to this system.
- **INT2: Koha (API - Optional):** May be used to fetch bibliographic data for books to speed up cataloging.
- **INT3: Barcode Scanner:** The system must interface with standard barcode scanners (likely as a keyboard wedge) for user IDs and book ISBNs.

### 7. Constraints & Assumptions

- **C1:** The project timeline is approximately **3 weeks** for the MVP.
- **C2:** The system will not handle online payments for fines.
- **C3:** The system will be developed in English only.
- **C4:** Audit logs will be retained for **3 months**.
- **A1:** The QCU Enrollment System has a stable and documented API available for integration.
- **A2:** A plan for admin onboarding and training will be developed post-MVP.

---

## 8. MVP (Minimum Viable Product) Scope

### Static Pages (Public Access):
1. **Home Page:** Welcome message, featured books, recent news/announcements, quick search
2. **About Page:** QCU Library history, mission, vision, staff information
3. **Services Page:** List of library services (borrowing, research assistance, facilities)
4. **Branches Page:** Information about library locations and their specific collections
5. **FAQ Page:** Common questions and answers about library policies and procedures
6. **Contact Page:** Library contact information, hours, location map

### Functional Features:

**For the Ordinary User:**
1. **Authentication:** Log in using Enrollment System credentials
2. **Book Search:** Search the catalog by title, author, or ISBN with results display
3. **View Book Details:** See book availability, description, and location
4. **Borrowing (Physical):** Have a book checked out to them by a librarian using barcodes
5. **View My Account:** See a list of current loans, due dates, and any outstanding fines

**For the Librarian (Admin):**
1. **Circulation Desk:**
   - Check out books to users by scanning the user's ID and the book's ISBN
   - Check in books by scanning the book's ISBN
2. **Basic Cataloging:** Manually add a new book to the system (Title, Author, ISBN, Description)
3. **Fine Management:** View and clear fines for a user when they pay at the desk
4. **Content Management:** Create and edit content for static pages (About, Services, etc.)

**For the Head Librarian (Super Admin):**
1. **View Audit Logs:** Access a simple, chronological log of all major transactions (check-ins/outs)
2. **Content Approval:** Review and approve page content changes

### Features NOT in MVP (Future Development):
- Online holds/reservations for books
- Room reservations system
- Events/Announcements/Newsletter functionality
- Advanced search filters and sorting
- Automated email notifications
- Head Librarian's analytics dashboard
- Password reset functionality
- API integration for pushing fines to the enrollment system
