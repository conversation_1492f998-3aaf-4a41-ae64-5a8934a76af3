# QCU OPAC MVP Task Backlog

**Project Timeline:** 3 weeks
**Team Structure:** Full-stack development approach### Featured Books from Design
```javascript
{
  id: "BK001",
  title: "The 48 Laws of Power",
  author: "<PERSON>",
  coverImage: "/covers/48-laws-power.jpg",
  category: "Self-Help",
  isNewAcquisition: true
},
{
  id: "BK002", 
  title: "Life And Works Of Rizal",
  author: "Dr. <PERSON>",
  coverImage: "/covers/rizal-life-works.jpg",
  category: "Philippine Literature",
  isNewAcquisition: true
},
{
  id: "BK003",
  title: "ABNKKBSNPLAKo?!",
  author: "<PERSON>g",
  coverImage: "/covers/abnkkbsnplako.jpg",
  category: "Filipino Humor",
  isNewAcquisition: true
}k 1: Foundation & Frontend with Mock Data

### Project Setup & Mock Data (Days 1-2)
- [ ] **SETUP-001:** Initialize React project with routing and state management
- [ ] **SETUP-002:** Set up responsive design system (Tailwind CSS)
- [ ] **MOCK-001:** Create comprehensive mock data for books (50+ sample books)
- [ ] **MOCK-002:** Create mock user data and authentication states
- [ ] **MOCK-003:** Create mock data for loans, fines, and transactions
- [ ] **MOCK-004:** Set up mock API layer for development

### Core Pages Development (Days 3-5)
- [ ] **PAGE-001:** Build Homepage header with QCU logo, branding, and building image
- [ ] **PAGE-002:** Create navigation bar with all menu items and dropdowns
- [ ] **PAGE-003:** Build hero section with library background, title, and search bar
- [ ] **PAGE-004:** Create "New Acquisitions" section with book carousel display
- [ ] **PAGE-005:** Build "Our Services" section with 3 service cards
- [ ] **PAGE-006:** Create "Announcements" section with news cards
- [ ] **PAGE-007:** Build comprehensive footer with QCU info and links

---

## Week 2: User Features & Catalog System

### Authentication & User Management (Days 6-7)
- [ ] **AUTH-001:** Create login page with mock authentication
- [ ] **AUTH-002:** Implement user dashboard/profile page
- [ ] **AUTH-003:** Set up role-based routing (Student, Librarian, Head Librarian)
- [ ] **AUTH-004:** Create protected routes and access control
- [ ] **AUTH-005:** Build user account page showing loans and fines

### Book Catalog & Search (Days 8-10)
- [ ] **CAT-001:** Build book search page with filters (title, author, subject)
- [ ] **CAT-002:** Create book results grid/list view with cover images
- [ ] **CAT-003:** Implement book details page with full information
- [ ] **CAT-004:** Add book availability status indicators
- [ ] **CAT-005:** Create advanced search with multiple criteria
- [ ] **CAT-006:** Implement search suggestions and auto-complete
- [ ] **CAT-007:** Build featured books carousel for homepage

---

## Week 3: Admin Interface & Polish

### Librarian Dashboard (Days 11-12)
- [ ] **ADMIN-001:** Create librarian circulation desk interface
- [ ] **ADMIN-002:** Build book management (add, edit, delete) with mock data
- [ ] **ADMIN-003:** Create user lookup and account management
- [ ] **ADMIN-004:** Build transaction history and fine management
- [ ] **ADMIN-005:** Implement basic reporting dashboard
- [ ] **ADMIN-006:** Create content management for static pages

### Head Librarian Features (Day 13)
- [ ] **SUPER-001:** Build comprehensive admin dashboard with statistics
- [ ] **SUPER-002:** Create audit log viewing interface
- [ ] **SUPER-003:** Implement system settings management
- [ ] **SUPER-004:** Build user role management interface

### Final Polish & Testing (Days 14-15)
- [ ] **POLISH-001:** Optimize responsive design for all screen sizes
- [ ] **POLISH-002:** Implement loading states and error handling
- [ ] **POLISH-003:** Add animations and smooth transitions
- [ ] **POLISH-004:** Perform comprehensive testing of all features
- [ ] **POLISH-005:** Create demo data scenarios for presentation
- [ ] **POLISH-006:** Documentation and deployment preparation

---

## Page Structure & Components

### Public Pages (No Authentication Required)
- **Homepage (`/`):** 
  - Header with QCU branding and building image
  - Navigation bar (Home, Catalog, Services, Branches, About Us, News & Events, Help, Library Account)
  - Hero section with "QCU Digital Library" title and search bar
  - "New Acquisitions" section with book covers carousel
  - "Our Services" section with 3 cards (Digital Library, Book Circulation, Collaborative Room)
  - "Announcements" section with 3 announcement cards
  - Footer with QCU info, Quick Links, and Connect With Us sections
- **Catalog (`/catalog`):** Book search and browse interface with filters
- **Services (`/services`):** Digital library, circulation, room booking services
- **Branches (`/branches`):** Library locations (San Bartolome, San Francisco, Batasan)
- **About Us (`/about`):** QCU library history, mission, staff directory
- **News & Events (`/news`):** Announcements and events listing
- **Help (`/help`):** FAQ and support resources

### User Pages (Authentication Required)
- **Login (`/login`):** Sign-in form with enrollment system integration
- **My Account (`/account`):** Profile, current loans, fines, borrowing history
- **Book Search (`/search`):** Advanced search with filters, results display
- **Book Details (`/book/:id`):** Full book information, availability, related books

### Admin Pages (Librarian Role)
- **Circulation Desk (`/admin/circulation`):** Check-in/out interface, user lookup
- **Book Management (`/admin/books`):** Add, edit, delete books, inventory management
- **User Management (`/admin/users`):** View user accounts, manage fines, account status
- **Reports (`/admin/reports`):** Basic statistics, overdue items, popular books

### Super Admin Pages (Head Librarian Role)
- **Dashboard (`/admin/dashboard`):** System overview, key metrics, quick actions
- **Audit Logs (`/admin/logs`):** System activity tracking, user actions
- **Settings (`/admin/settings`):** System configuration, content management
- **User Roles (`/admin/roles`):** Manage librarian accounts and permissions

---

## Mock Data Requirements

### Book Data (50+ sample books)
```javascript
{
  id: "BK001",
  isbn: "978-**********",
  title: "Introduction to Computer Science",
  author: "John Smith",
  subject: "Computer Science",
  publisher: "Tech Books Ltd",
  year: 2023,
  copies: 3,
  available: 2,
  location: "CS Section - 2nd Floor",
  coverImage: "/covers/cs-intro.jpg",
  description: "A comprehensive guide to computer science fundamentals..."
}
```

### User Data
```javascript
{
  id: "2021-001234",
  name: "Maria Santos",
  program: "BS Computer Science",
  year: 3,
  email: "<EMAIL>",
  role: "student",
  currentLoans: 2,
  totalFines: 40.00
}
```

### Transaction Data
```javascript
{
  id: "TXN001",
  userId: "2021-001234",
  bookId: "BK001",
  type: "checkout",
  date: "2025-09-15",
  dueDate: "2025-09-29",
  status: "active"
}
```

---

## Key Design Components (Based on Figma)

### Header Section
- **QCU Logo:** Left-aligned university logo
- **University Name:** "Quezon City University" in golden (#e0aa14) Cinzel font
- **Campus Locations:** "San Bartolome • San Francisco • Batasan" subtitle
- **Building Image:** QCU building photo on the right side
- **White background with shadow border**

### Navigation Bar  
- **Menu Items:** Home, Catalog↓, Services↓, Branches↓, About Us, News & Events↓, Help, Library Account
- **Dropdowns:** Several items have dropdown arrows indicating submenus
- **Clean horizontal layout with proper spacing**

### Hero Section
- **Background:** Library interior image with dark overlay (rgba(57,66,97,0.65))
- **Main Title:** "QCU Digital Library" in large Kaisei Tokumin font
- **Subtitle:** "Discover knowledge, explore resources, and advance your academic journey"
- **Search Bar:** Full-width search with blue search button (#374fa1)
- **Tagline:** "Access thousands of academic resources at your fingertips"

### New Acquisitions Section
- **Title:** "NEW ACQUISITIONS" with "BOOKS" subtitle
- **Book Display:** 5 book covers in a horizontal layout with blur effects on edges
- **Featured Books:** The 48 Laws of Power, Life and Works of Rizal, ABNKKBSNPLAKo?!, and others
- **Typography:** Book titles in Inter Medium 18px

### Our Services Section
- **Background:** Gradient overlay with design elements
- **Title:** "OUR SERVICES" in Alata font
- **Three Service Cards:**
  1. Digital Library - "ACCESS THOUSAND OF BOOKS ONLINE"
  2. Book Circulation - "Borrow, renew, and return books with ease"
  3. Collaborative Room - "Work together, share ideas, and innovate"
- **Card Design:** Image backgrounds with white text and call-to-action buttons

### Announcements Section
- **Title:** "ANNOUNCEMENTS" in Inter Semi Bold 40px
- **Three Announcement Cards:** Each with image, date, title, excerpt, and "Read More" button
- **Sample Content:** "Calling All QCU Faculty: Apply for Global Research Microgant"
- **"View All Announcements" link** on the right

### Footer
- **QCU Logo and Branding** with golden color scheme
- **Quick Links:** Home, Catalog, Services, Branches, About Us, News & Events, Help
- **Connect With Us:** Facebook and email contact (<EMAIL>)
- **Library Hours:** Mon-Fri: 7:00 AM - 9:00 PM, Sat-Sun: 8:00 AM - 6:00 PM
- **Copyright and Legal Links:** Privacy Policy, Terms of Service, Accessibility

---

## Priority Levels

**High Priority (Must Have for MVP):**
- User authentication and book search
- Basic circulation (check-in/out)
- Static informational pages
- User account management

**Medium Priority (Important for Launch):**
- Barcode scanning integration  
- Fine management
- Content management system
- Audit logging

**Low Priority (Nice to Have):**
- Advanced search features
- Enhanced UI/UX polish
- Performance optimizations
- Extended error handling

---

## Technical Stack Recommendations

**Frontend:** React.js, React Router, Tailwind CSS/Material-UI
**Backend:** Node.js, Express.js, JWT authentication
**Database:** PostgreSQL or MySQL
**Additional:** Barcode scanner SDK/library, API documentation tools

---

## Success Criteria

✅ Users can log in using enrollment system credentials  
✅ Users can search and view book information  
✅ Librarians can check books in/out using barcodes  
✅ Basic fine management is functional  
✅ All static pages are informative and accessible  
✅ System is responsive on desktop (1440x1024)  
✅ Basic audit logging captures key transactions